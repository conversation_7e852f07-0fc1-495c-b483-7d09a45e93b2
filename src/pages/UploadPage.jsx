import { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { useAppStorage } from "../hooks/useAppStorage";
import { useGetAppApi } from "../hooks/useGetAppApi";
import { useAppUploadApi } from "../hooks/useAppUploadApi";
import { FileUpload } from "../components/shared/FileUpload";
import { validationSchema } from "../utils/validationSchema";
import { APP_FLOW_STATUS } from "../utils/consts";
import { LoaderCircle, CheckCircle, AlertCircle } from "lucide-react";

/**
 * Upload Page component for handling document uploads for applications in APP_DOCS_PENDING state
 */
export const UploadPage = () => {
  const { uuid } = useParams();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { applicationId, setApplicationId, clearAllData, setApplicationResult } = useAppStorage();

  // Initialize application ID if different from URL
  useEffect(() => {
    if (uuid !== applicationId) {
      clearAllData();
      setApplicationId(uuid);
    }
  }, [uuid, applicationId, setApplicationId, clearAllData]);

  // Fetch application data
  const {
    isLoading: isFetchingApp,
    isSuccess: isFetchSuccess,
    result: application,
    isError: isFetchError,
    error: fetchError,
  } = useGetAppApi(uuid, true, null);

  // Upload API hook
  const {
    uploadDocs,
    isLoading: isUploading,
    isSuccess: isUploadSuccess,
    isError: isUploadError,
    error: uploadError,
    result: uploadResult,
  } = useAppUploadApi();

  // Form setup
  const formMethods = useForm({
    defaultValues: {
      bankStatements: [],
    },
    mode: "onSubmit",
    reValidateMode: "onBlur",
  });

  // Handle application state redirects
  useEffect(() => {
    if (isFetchSuccess && application) {
      if (application.status !== APP_FLOW_STATUS.APP_DOCS_PENDING) {
        // Redirect based on application status
        switch (application.status) {
          case APP_FLOW_STATUS.PREQUAL_DENIED:
            navigate(`/prequalify-result/${uuid}`);
            break;
          case APP_FLOW_STATUS.APP_COMPLETED:
            navigate(`/application/${uuid}/result`);
            break;
          case APP_FLOW_STATUS.PREQUAL_APPROVED:
          case APP_FLOW_STATUS.APP_GENERATED:
          case APP_FLOW_STATUS.APP_STARTED:
          case APP_FLOW_STATUS.APP_SUBMITTED:
          case APP_FLOW_STATUS.APP_EDITING:
          case APP_FLOW_STATUS.APP_SIGNED:
            navigate(`/application/${uuid}`);
            break;
          default:
            navigate(`/application/${uuid}`);
        }
      }
    }
  }, [isFetchSuccess, application, navigate, uuid]);

  // Handle successful upload
  useEffect(() => {
    if (isUploadSuccess && uploadResult) {
      setApplicationResult(uploadResult);
      navigate(`/application/${uuid}/result`);
    }
  }, [isUploadSuccess, uploadResult, setApplicationResult, navigate, uuid]);

  // Handle form submission
  const handleSubmit = async (data) => {
    setIsSubmitting(true);

    try {
      await uploadDocs(uuid, {
        bankStatements: data.bankStatements,
      });
    } catch (error) {
      console.error("Error uploading documents:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Loading state
  if (isFetchingApp) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <div className="flex items-center justify-center py-12">
          <LoaderCircle className="h-8 w-8 animate-spin text-blue-500" />
          <span className="ml-2 text-gray-600">Loading application...</span>
        </div>
      </div>
    );
  }

  // Error state
  if (isFetchError) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <div className="bg-red-50 border border-red-200 rounded-md p-6 text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-red-800 mb-2">Error Loading Application</h2>
          <p className="text-red-600 mb-4">{fetchError || "Unable to load application details"}</p>
          <button
            onClick={() => navigate("/")}
            className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition"
          >
            Return to Home
          </button>
        </div>
      </div>
    );
  }

  // Main upload form
  return (
    <div className="max-w-2xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
        <div className="text-center mb-8">
          <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Upload Bank Statements</h1>
          <p className="text-gray-600">Please upload your bank statements for faster processing.</p>
        </div>

        <form onSubmit={formMethods.handleSubmit(handleSubmit)} className="space-y-6">
          <FileUpload
            name="bankStatements"
            label="Bank Statements (Last 3-6 months)"
            control={formMethods.control}
            rules={validationSchema.bankStatements}
            acceptedFileTypes={["application/pdf"]}
            maxSize={2 * 1024 * 1024} // 2MB
            minFiles={3}
            maxFiles={6}
          />

          {/* Upload error */}
          {isUploadError && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <AlertCircle className="h-5 w-5 text-red-400 mt-0.5" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Upload Failed</h3>
                  <p className="text-sm text-red-700 mt-1">{uploadError}</p>
                </div>
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={() => navigate(`/application/${uuid}`)}
              className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition"
              disabled={isSubmitting || isUploading}
            >
              Back to Application
            </button>
            <button
              type="submit"
              disabled={isSubmitting || isUploading}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition flex items-center"
            >
              {(isSubmitting || isUploading) && <LoaderCircle className="h-4 w-4 animate-spin mr-2" />}
              {isSubmitting || isUploading ? "Uploading..." : "Submit Documents"}
            </button>
          </div>
        </form>

        <div className="mt-6 text-sm text-gray-500 text-center">
          <p>Your documents are securely encrypted and will only be used for underwriting purposes.</p>
        </div>
      </div>
    </div>
  );
};

export default UploadPage;
