import { useNavigate, useParams } from "react-router-dom";
import { SkeletonLoading } from "./SkeletonLoading";
import { ErrorState } from "./ErrorState";
import { ApplicationSuccess } from "./ApplicationSuccess";
import { useAppStorage } from "../../hooks/useAppStorage.js";
import { useGetAppApi } from "../../hooks/useGetAppApi.js";
import { useEffect } from "react";
import { isAppCompletedOrDocsPending } from "../../utils/consts.js";

/**
 * Application result component that shows the application success page
 * @returns {JSX.Element}
 */
export const ApplicationResult = () => {
  const navigate = useNavigate();
  const { uuid: appUuid } = useParams();
  const { applicationResult, setApplicationResult, applicationId, setApplicationId } = useAppStorage();

  if (appUuid != applicationId) {
    setApplicationId(appUuid);
    setApplicationResult(null);
  }

  // Use our custom hook to fetch the application data
  const { isLoading, isSuccess, error, errorId, result: application } = useGetAppApi(appUuid, true, applicationResult);

  useEffect(() => {
    // smoothly scroll to the top of the page
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, []);

  useEffect(() => {
    if (isSuccess && application) {
      setApplicationResult(application);

      if (!isAppCompletedOrDocsPending(application)) {
        navigate(`/application/${application?.uuid}`);
      }
    }
  }, [application, navigate, isSuccess, setApplicationResult]);

  if (!isLoading && error) {
    return <ErrorState message={error} errorId={errorId} />;
  }

  if (isAppCompletedOrDocsPending(application)) {
    return <ApplicationSuccess application={applicationResult} />;
  }

  if (isLoading || !application || !isAppCompletedOrDocsPending(application)) {
    return <SkeletonLoading />;
  }
};

export default ApplicationResult;
